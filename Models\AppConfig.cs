using System;
using System.Collections.Generic;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// 应用程序主配置类
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 路径设置
        /// </summary>
        public PathSettings PathSettings { get; set; } = new PathSettings();

        /// <summary>
        /// 处理设置
        /// </summary>
        public ProcessSettings ProcessSettings { get; set; } = new ProcessSettings();

        /// <summary>
        /// 功能启用状态
        /// </summary>
        public Dictionary<string, bool> FunctionEnabled { get; set; } = new Dictionary<string, bool>();

        /// <summary>
        /// 日志设置
        /// </summary>
        public LogSettings LogSettings { get; set; } = new LogSettings();

        /// <summary>
        /// 窗体设置
        /// </summary>
        public FormSettings FormSettings { get; set; } = new FormSettings();
    }

    /// <summary>
    /// 路径设置
    /// </summary>
    public class PathSettings
    {
        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubfolders { get; set; } = true;

        /// <summary>
        /// 是否保持目录结构
        /// </summary>
        public bool KeepDirectoryStructure { get; set; } = true;
    }

    /// <summary>
    /// 处理设置
    /// </summary>
    public class ProcessSettings
    {
        /// <summary>
        /// 是否复制文件（true=复制，false=移动）
        /// </summary>
        public bool CopyFiles { get; set; } = true;

        /// <summary>
        /// 是否直接处理源文件
        /// </summary>
        public bool ProcessSourceDirectly { get; set; } = false;

        /// <summary>
        /// 线程数
        /// </summary>
        public int ThreadCount { get; set; } = 1;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 批处理大小
        /// </summary>
        public int BatchSize { get; set; } = 50;

        /// <summary>
        /// 支持的文件格式
        /// </summary>
        public List<string> SupportedFormats { get; set; } = new List<string>
        {
            ".ppt", ".pptx", ".pptm", ".ppsx", ".ppsm", ".potx", ".potm"
        };
    }

    /// <summary>
    /// 日志设置
    /// </summary>
    public class LogSettings
    {
        /// <summary>
        /// 是否启用日志
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Info;

        /// <summary>
        /// 启用的日志类型
        /// </summary>
        public Dictionary<string, bool> EnabledLogTypes { get; set; } = new Dictionary<string, bool>
        {
            { "处理开始", true },
            { "处理完成", true },
            { "处理错误", true },
            { "文件操作", true },
            { "配置变更", false },
            { "调试信息", false }
        };

        /// <summary>
        /// 日志文件最大大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 10;

        /// <summary>
        /// 保留日志文件天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 30;
    }

    /// <summary>
    /// 窗体设置
    /// </summary>
    public class FormSettings
    {
        /// <summary>
        /// 窗体位置X
        /// </summary>
        public int LocationX { get; set; } = -1;

        /// <summary>
        /// 窗体位置Y
        /// </summary>
        public int LocationY { get; set; } = -1;

        /// <summary>
        /// 窗体宽度
        /// </summary>
        public int Width { get; set; } = 1024;

        /// <summary>
        /// 窗体高度
        /// </summary>
        public int Height { get; set; } = 768;

        /// <summary>
        /// 窗体状态
        /// </summary>
        public int WindowState { get; set; } = 0; // Normal
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3
    }

    /// <summary>
    /// 处理选项
    /// </summary>
    public class ProcessingOptions
    {
        public string SourcePath { get; set; } = string.Empty;
        public string OutputPath { get; set; } = string.Empty;
        public bool IncludeSubfolders { get; set; } = true;
        public bool KeepDirectoryStructure { get; set; } = true;
        public bool CopyFiles { get; set; } = true;
        public bool ProcessSourceDirectly { get; set; } = false;
        public int ThreadCount { get; set; } = 1;
        public int RetryCount { get; set; } = 3;
        public int BatchSize { get; set; } = 50;
        public List<string> SupportedFormats { get; set; } = new List<string>();
    }

    /// <summary>
    /// 处理统计信息
    /// </summary>
    public class ProcessingStats
    {
        public int TotalFiles { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }

        public double SuccessRate => TotalFiles > 0 ? (double)SuccessCount / TotalFiles * 100 : 0;
        public double FailureRate => TotalFiles > 0 ? (double)FailureCount / TotalFiles * 100 : 0;
        public TimeSpan ElapsedTime => (EndTime ?? DateTime.Now) - StartTime;
        public double FilesPerMinute => ElapsedTime.TotalMinutes > 0 ? (SuccessCount + FailureCount) / ElapsedTime.TotalMinutes : 0;
    }

    /// <summary>
    /// 处理进度事件参数
    /// </summary>
    public class ProcessProgressEventArgs : EventArgs
    {
        public int ProgressPercentage { get; }
        public ProcessingStats Stats { get; }

        public ProcessProgressEventArgs(int progressPercentage, ProcessingStats stats)
        {
            ProgressPercentage = progressPercentage;
            Stats = stats;
        }
    }

    /// <summary>
    /// 处理完成事件参数
    /// </summary>
    public class ProcessCompletedEventArgs : EventArgs
    {
        public ProcessingStats Stats { get; }
        public string Message { get; }

        public ProcessCompletedEventArgs(ProcessingStats stats, string message)
        {
            Stats = stats;
            Message = message;
        }
    }

    /// <summary>
    /// 文件处理事件参数
    /// </summary>
    public class FileProcessedEventArgs : EventArgs
    {
        public string FileName { get; }
        public bool Success { get; }
        public string? ErrorMessage { get; }

        public FileProcessedEventArgs(string fileName, bool success, string? errorMessage)
        {
            FileName = fileName;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }
}
