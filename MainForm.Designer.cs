namespace PPTPiliangChuli
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.操作ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.设置ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.帮助ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.groupBoxPaths = new System.Windows.Forms.GroupBox();
            this.btnBrowseOutput = new System.Windows.Forms.Button();
            this.btnBrowseSource = new System.Windows.Forms.Button();
            this.chkKeepStructure = new System.Windows.Forms.CheckBox();
            this.chkIncludeSubfolders = new System.Windows.Forms.CheckBox();
            this.txtOutputPath = new System.Windows.Forms.TextBox();
            this.txtSourcePath = new System.Windows.Forms.TextBox();
            this.lblOutputPath = new System.Windows.Forms.Label();
            this.lblSourcePath = new System.Windows.Forms.Label();
            this.groupBoxSettings = new System.Windows.Forms.GroupBox();
            this.btnSupportedFormats = new System.Windows.Forms.Button();
            this.btnSelectAll = new System.Windows.Forms.Button();
            this.btnDeselectAll = new System.Windows.Forms.Button();
            this.numBatchSize = new System.Windows.Forms.NumericUpDown();
            this.lblBatchSize = new System.Windows.Forms.Label();
            this.numRetryCount = new System.Windows.Forms.NumericUpDown();
            this.lblRetryCount = new System.Windows.Forms.Label();
            this.numThreadCount = new System.Windows.Forms.NumericUpDown();
            this.lblThreadCount = new System.Windows.Forms.Label();
            this.chkDeleteSource = new System.Windows.Forms.CheckBox();
            this.radioMove = new System.Windows.Forms.RadioButton();
            this.radioCopy = new System.Windows.Forms.RadioButton();
            this.lblConflictHandling = new System.Windows.Forms.Label();
            this.groupBoxFunctions = new System.Windows.Forms.GroupBox();
            this.tableLayoutPanelFunctions = new System.Windows.Forms.TableLayoutPanel();
            this.groupBoxActions = new System.Windows.Forms.GroupBox();
            this.tableLayoutPanelActions = new System.Windows.Forms.TableLayoutPanel();
            this.groupBoxStats = new System.Windows.Forms.GroupBox();
            this.progressBar = new System.Windows.Forms.ProgressBar();
            this.lblStats = new System.Windows.Forms.Label();
            this.lblProgress = new System.Windows.Forms.Label();
            this.menuStrip1.SuspendLayout();
            this.groupBoxPaths.SuspendLayout();
            this.groupBoxSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBatchSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRetryCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThreadCount)).BeginInit();
            this.groupBoxFunctions.SuspendLayout();
            this.groupBoxActions.SuspendLayout();
            this.groupBoxStats.SuspendLayout();
            this.SuspendLayout();
            //
            // menuStrip1
            //
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.文件ToolStripMenuItem,
            this.操作ToolStripMenuItem,
            this.设置ToolStripMenuItem,
            this.帮助ToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(1024, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";
            //
            // 文件ToolStripMenuItem
            //
            this.文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
            this.文件ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.文件ToolStripMenuItem.Text = "文件";
            //
            // 操作ToolStripMenuItem
            //
            this.操作ToolStripMenuItem.Name = "操作ToolStripMenuItem";
            this.操作ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.操作ToolStripMenuItem.Text = "操作";
            //
            // 设置ToolStripMenuItem
            //
            this.设置ToolStripMenuItem.Name = "设置ToolStripMenuItem";
            this.设置ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.设置ToolStripMenuItem.Text = "设置";
            //
            // 帮助ToolStripMenuItem
            //
            this.帮助ToolStripMenuItem.Name = "帮助ToolStripMenuItem";
            this.帮助ToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
            this.帮助ToolStripMenuItem.Text = "帮助";
            //
            // groupBoxPaths
            //
            this.groupBoxPaths.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxPaths.Controls.Add(this.btnBrowseOutput);
            this.groupBoxPaths.Controls.Add(this.btnBrowseSource);
            this.groupBoxPaths.Controls.Add(this.chkKeepStructure);
            this.groupBoxPaths.Controls.Add(this.chkIncludeSubfolders);
            this.groupBoxPaths.Controls.Add(this.txtOutputPath);
            this.groupBoxPaths.Controls.Add(this.txtSourcePath);
            this.groupBoxPaths.Controls.Add(this.lblOutputPath);
            this.groupBoxPaths.Controls.Add(this.lblSourcePath);
            this.groupBoxPaths.Location = new System.Drawing.Point(12, 27);
            this.groupBoxPaths.Name = "groupBoxPaths";
            this.groupBoxPaths.Size = new System.Drawing.Size(1000, 80);
            this.groupBoxPaths.TabIndex = 1;
            this.groupBoxPaths.TabStop = false;
            this.groupBoxPaths.Text = "路径设置";
            //
            // btnBrowseOutput
            //
            this.btnBrowseOutput.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBrowseOutput.Location = new System.Drawing.Point(940, 48);
            this.btnBrowseOutput.Name = "btnBrowseOutput";
            this.btnBrowseOutput.Size = new System.Drawing.Size(54, 23);
            this.btnBrowseOutput.TabIndex = 7;
            this.btnBrowseOutput.Text = "浏览...";
            this.btnBrowseOutput.UseVisualStyleBackColor = true;
            //
            // btnBrowseSource
            //
            this.btnBrowseSource.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBrowseSource.Location = new System.Drawing.Point(940, 19);
            this.btnBrowseSource.Name = "btnBrowseSource";
            this.btnBrowseSource.Size = new System.Drawing.Size(54, 23);
            this.btnBrowseSource.TabIndex = 6;
            this.btnBrowseSource.Text = "浏览...";
            this.btnBrowseSource.UseVisualStyleBackColor = true;
            //
            // chkKeepStructure
            //
            this.chkKeepStructure.AutoSize = true;
            this.chkKeepStructure.Location = new System.Drawing.Point(830, 50);
            this.chkKeepStructure.Name = "chkKeepStructure";
            this.chkKeepStructure.Size = new System.Drawing.Size(99, 19);
            this.chkKeepStructure.TabIndex = 5;
            this.chkKeepStructure.Text = "保持目录结构";
            this.chkKeepStructure.UseVisualStyleBackColor = true;
            //
            // chkIncludeSubfolders
            //
            this.chkIncludeSubfolders.AutoSize = true;
            this.chkIncludeSubfolders.Location = new System.Drawing.Point(830, 21);
            this.chkIncludeSubfolders.Name = "chkIncludeSubfolders";
            this.chkIncludeSubfolders.Size = new System.Drawing.Size(87, 19);
            this.chkIncludeSubfolders.TabIndex = 4;
            this.chkIncludeSubfolders.Text = "包含子目录";
            this.chkIncludeSubfolders.UseVisualStyleBackColor = true;
            //
            // txtOutputPath
            //
            this.txtOutputPath.Location = new System.Drawing.Point(112, 48);
            this.txtOutputPath.Name = "txtOutputPath";
            this.txtOutputPath.Size = new System.Drawing.Size(712, 23);
            this.txtOutputPath.TabIndex = 3;
            this.txtOutputPath.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            //
            // txtSourcePath
            //
            this.txtSourcePath.Location = new System.Drawing.Point(112, 19);
            this.txtSourcePath.Name = "txtSourcePath";
            this.txtSourcePath.Size = new System.Drawing.Size(712, 23);
            this.txtSourcePath.TabIndex = 2;
            this.txtSourcePath.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            //
            // lblOutputPath
            //
            this.lblOutputPath.AutoSize = true;
            this.lblOutputPath.Location = new System.Drawing.Point(6, 51);
            this.lblOutputPath.Name = "lblOutputPath";
            this.lblOutputPath.Size = new System.Drawing.Size(100, 15);
            this.lblOutputPath.TabIndex = 1;
            this.lblOutputPath.Text = "输出目录：";
            //
            // lblSourcePath
            //
            this.lblSourcePath.AutoSize = true;
            this.lblSourcePath.Location = new System.Drawing.Point(6, 22);
            this.lblSourcePath.Name = "lblSourcePath";
            this.lblSourcePath.Size = new System.Drawing.Size(100, 15);
            this.lblSourcePath.TabIndex = 0;
            this.lblSourcePath.Text = "来源目录：";
            //
            // groupBoxSettings
            //
            this.groupBoxSettings.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxSettings.Controls.Add(this.btnSupportedFormats);
            this.groupBoxSettings.Controls.Add(this.lblThreadCount);
            this.groupBoxSettings.Controls.Add(this.numThreadCount);
            this.groupBoxSettings.Controls.Add(this.lblRetryCount);
            this.groupBoxSettings.Controls.Add(this.numRetryCount);
            this.groupBoxSettings.Controls.Add(this.lblBatchSize);
            this.groupBoxSettings.Controls.Add(this.numBatchSize);
            this.groupBoxSettings.Controls.Add(this.lblConflictHandling);
            this.groupBoxSettings.Controls.Add(this.radioCopy);
            this.groupBoxSettings.Controls.Add(this.radioMove);
            this.groupBoxSettings.Controls.Add(this.chkDeleteSource);
            this.groupBoxSettings.Controls.Add(this.btnSelectAll);
            this.groupBoxSettings.Controls.Add(this.btnDeselectAll);
            this.groupBoxSettings.Location = new System.Drawing.Point(12, 113);
            this.groupBoxSettings.Name = "groupBoxSettings";
            this.groupBoxSettings.Size = new System.Drawing.Size(1000, 100);
            this.groupBoxSettings.TabIndex = 2;
            this.groupBoxSettings.TabStop = false;
            this.groupBoxSettings.Text = "处理设置";
            //
            // btnSupportedFormats
            //
            this.btnSupportedFormats.Location = new System.Drawing.Point(10, 25);
            this.btnSupportedFormats.Name = "btnSupportedFormats";
            this.btnSupportedFormats.Size = new System.Drawing.Size(100, 25);
            this.btnSupportedFormats.TabIndex = 12;
            this.btnSupportedFormats.Text = "支持格式设定";
            this.btnSupportedFormats.UseVisualStyleBackColor = true;
            //
            // lblThreadCount
            //
            this.lblThreadCount.AutoSize = true;
            this.lblThreadCount.Location = new System.Drawing.Point(130, 30);
            this.lblThreadCount.Name = "lblThreadCount";
            this.lblThreadCount.Size = new System.Drawing.Size(56, 15);
            this.lblThreadCount.TabIndex = 4;
            this.lblThreadCount.Text = "线程数:";
            //
            // numThreadCount
            //
            this.numThreadCount.Location = new System.Drawing.Point(190, 27);
            this.numThreadCount.Maximum = new decimal(new int[] {
            16,
            0,
            0,
            0});
            this.numThreadCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numThreadCount.Name = "numThreadCount";
            this.numThreadCount.Size = new System.Drawing.Size(60, 23);
            this.numThreadCount.TabIndex = 5;
            this.numThreadCount.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            //
            // lblRetryCount
            //
            this.lblRetryCount.AutoSize = true;
            this.lblRetryCount.Location = new System.Drawing.Point(270, 30);
            this.lblRetryCount.Name = "lblRetryCount";
            this.lblRetryCount.Size = new System.Drawing.Size(68, 15);
            this.lblRetryCount.TabIndex = 6;
            this.lblRetryCount.Text = "重试次数:";
            //
            // numRetryCount
            //
            this.numRetryCount.Location = new System.Drawing.Point(344, 27);
            this.numRetryCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numRetryCount.Name = "numRetryCount";
            this.numRetryCount.Size = new System.Drawing.Size(60, 23);
            this.numRetryCount.TabIndex = 7;
            this.numRetryCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            //
            // lblBatchSize
            //
            this.lblBatchSize.AutoSize = true;
            this.lblBatchSize.Location = new System.Drawing.Point(424, 30);
            this.lblBatchSize.Name = "lblBatchSize";
            this.lblBatchSize.Size = new System.Drawing.Size(80, 15);
            this.lblBatchSize.TabIndex = 8;
            this.lblBatchSize.Text = "批处理数量:";
            //
            // numBatchSize
            //
            this.numBatchSize.Location = new System.Drawing.Point(510, 27);
            this.numBatchSize.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numBatchSize.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numBatchSize.Name = "numBatchSize";
            this.numBatchSize.Size = new System.Drawing.Size(60, 23);
            this.numBatchSize.TabIndex = 9;
            this.numBatchSize.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            //
            // lblConflictHandling
            //
            this.lblConflictHandling.AutoSize = true;
            this.lblConflictHandling.Location = new System.Drawing.Point(10, 65);
            this.lblConflictHandling.Name = "lblConflictHandling";
            this.lblConflictHandling.Size = new System.Drawing.Size(68, 15);
            this.lblConflictHandling.TabIndex = 0;
            this.lblConflictHandling.Text = "冲突处理:";
            //
            // radioCopy
            //
            this.radioCopy.AutoSize = true;
            this.radioCopy.Checked = true;
            this.radioCopy.Location = new System.Drawing.Point(84, 63);
            this.radioCopy.Name = "radioCopy";
            this.radioCopy.Size = new System.Drawing.Size(50, 19);
            this.radioCopy.TabIndex = 1;
            this.radioCopy.TabStop = true;
            this.radioCopy.Text = "复制";
            this.radioCopy.UseVisualStyleBackColor = true;
            //
            // radioMove
            //
            this.radioMove.AutoSize = true;
            this.radioMove.Location = new System.Drawing.Point(140, 63);
            this.radioMove.Name = "radioMove";
            this.radioMove.Size = new System.Drawing.Size(50, 19);
            this.radioMove.TabIndex = 2;
            this.radioMove.Text = "移动";
            this.radioMove.UseVisualStyleBackColor = true;
            //
            // chkDeleteSource
            //
            this.chkDeleteSource.AutoSize = true;
            this.chkDeleteSource.Location = new System.Drawing.Point(210, 63);
            this.chkDeleteSource.Name = "chkDeleteSource";
            this.chkDeleteSource.Size = new System.Drawing.Size(111, 19);
            this.chkDeleteSource.TabIndex = 3;
            this.chkDeleteSource.Text = "直接处理源文件";
            this.chkDeleteSource.UseVisualStyleBackColor = true;
            //
            // btnSelectAll
            //
            this.btnSelectAll.Location = new System.Drawing.Point(330, 60);
            this.btnSelectAll.Name = "btnSelectAll";
            this.btnSelectAll.Size = new System.Drawing.Size(70, 25);
            this.btnSelectAll.TabIndex = 11;
            this.btnSelectAll.Text = "全选";
            this.btnSelectAll.UseVisualStyleBackColor = true;
            //
            // btnDeselectAll
            //
            this.btnDeselectAll.Location = new System.Drawing.Point(405, 60);
            this.btnDeselectAll.Name = "btnDeselectAll";
            this.btnDeselectAll.Size = new System.Drawing.Size(70, 25);
            this.btnDeselectAll.TabIndex = 10;
            this.btnDeselectAll.Text = "取消全选";
            this.btnDeselectAll.UseVisualStyleBackColor = true;
            //
            // groupBoxFunctions
            //
            this.groupBoxFunctions.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxFunctions.Controls.Add(this.tableLayoutPanelFunctions);
            this.groupBoxFunctions.Location = new System.Drawing.Point(12, 219);
            this.groupBoxFunctions.Name = "groupBoxFunctions";
            this.groupBoxFunctions.Size = new System.Drawing.Size(1000, 200);
            this.groupBoxFunctions.TabIndex = 3;
            this.groupBoxFunctions.TabStop = false;
            this.groupBoxFunctions.Text = "功能选择";
            //
            // tableLayoutPanelFunctions
            //
            this.tableLayoutPanelFunctions.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanelFunctions.ColumnCount = 3;
            this.tableLayoutPanelFunctions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.Location = new System.Drawing.Point(6, 22);
            this.tableLayoutPanelFunctions.Name = "tableLayoutPanelFunctions";
            this.tableLayoutPanelFunctions.RowCount = 3;
            this.tableLayoutPanelFunctions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelFunctions.Size = new System.Drawing.Size(988, 172);
            this.tableLayoutPanelFunctions.TabIndex = 0;
            //
            // groupBoxActions
            //
            this.groupBoxActions.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxActions.Controls.Add(this.tableLayoutPanelActions);
            this.groupBoxActions.Location = new System.Drawing.Point(12, 425);
            this.groupBoxActions.Name = "groupBoxActions";
            this.groupBoxActions.Size = new System.Drawing.Size(1000, 200);
            this.groupBoxActions.TabIndex = 4;
            this.groupBoxActions.TabStop = false;
            this.groupBoxActions.Text = "操作控制";
            //
            // tableLayoutPanelActions
            //
            this.tableLayoutPanelActions.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanelActions.ColumnCount = 3;
            this.tableLayoutPanelActions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.Location = new System.Drawing.Point(6, 22);
            this.tableLayoutPanelActions.Name = "tableLayoutPanelActions";
            this.tableLayoutPanelActions.RowCount = 3;
            this.tableLayoutPanelActions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 33.33333F));
            this.tableLayoutPanelActions.Size = new System.Drawing.Size(988, 172);
            this.tableLayoutPanelActions.TabIndex = 0;
            //
            // groupBoxStats
            //
            this.groupBoxStats.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBoxStats.Controls.Add(this.progressBar);
            this.groupBoxStats.Controls.Add(this.lblStats);
            this.groupBoxStats.Controls.Add(this.lblProgress);
            this.groupBoxStats.Location = new System.Drawing.Point(12, 631);
            this.groupBoxStats.Name = "groupBoxStats";
            this.groupBoxStats.Size = new System.Drawing.Size(1000, 120);
            this.groupBoxStats.TabIndex = 5;
            this.groupBoxStats.TabStop = false;
            this.groupBoxStats.Text = "处理统计";
            //
            // progressBar
            //
            this.progressBar.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.progressBar.Location = new System.Drawing.Point(6, 51);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new System.Drawing.Size(988, 23);
            this.progressBar.TabIndex = 2;
            //
            // lblStats
            //
            this.lblStats.AutoSize = true;
            this.lblStats.Location = new System.Drawing.Point(6, 22);
            this.lblStats.Name = "lblStats";
            this.lblStats.Size = new System.Drawing.Size(500, 15);
            this.lblStats.TabIndex = 1;
            this.lblStats.Text = "总文件数: 0  成功处理: 0 (0.0%)  处理失败: 0 (0.0%)  开始时间: --  预计结束时间: --";
            //
            // lblProgress
            //
            this.lblProgress.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblProgress.Location = new System.Drawing.Point(6, 80);
            this.lblProgress.Name = "lblProgress";
            this.lblProgress.Size = new System.Drawing.Size(988, 15);
            this.lblProgress.TabIndex = 0;
            this.lblProgress.Text = "处理速度: 0 文件/分钟    处理耗时: 00:00:00";
            //
            // MainForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1024, 763);
            this.Controls.Add(this.groupBoxStats);
            this.Controls.Add(this.groupBoxActions);
            this.Controls.Add(this.groupBoxFunctions);
            this.Controls.Add(this.groupBoxSettings);
            this.Controls.Add(this.groupBoxPaths);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.MinimumSize = new System.Drawing.Size(1040, 800);
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "PPT批量处理工具";
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.groupBoxPaths.ResumeLayout(false);
            this.groupBoxPaths.PerformLayout();
            this.groupBoxSettings.ResumeLayout(false);
            this.groupBoxSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBatchSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numRetryCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numThreadCount)).EndInit();
            this.groupBoxFunctions.ResumeLayout(false);
            this.groupBoxActions.ResumeLayout(false);
            this.groupBoxStats.ResumeLayout(false);
            this.groupBoxStats.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private MenuStrip menuStrip1;
        private ToolStripMenuItem 文件ToolStripMenuItem;
        private ToolStripMenuItem 操作ToolStripMenuItem;
        private ToolStripMenuItem 设置ToolStripMenuItem;
        private ToolStripMenuItem 帮助ToolStripMenuItem;
        private GroupBox groupBoxPaths;
        private Button btnBrowseOutput;
        private Button btnBrowseSource;
        private CheckBox chkKeepStructure;
        private CheckBox chkIncludeSubfolders;
        private TextBox txtOutputPath;
        private TextBox txtSourcePath;
        private Label lblOutputPath;
        private Label lblSourcePath;
        private GroupBox groupBoxSettings;
        private Button btnSelectAll;
        private Button btnDeselectAll;
        private NumericUpDown numBatchSize;
        private Label lblBatchSize;
        private NumericUpDown numRetryCount;
        private Label lblRetryCount;
        private NumericUpDown numThreadCount;
        private Label lblThreadCount;
        private CheckBox chkDeleteSource;
        private RadioButton radioMove;
        private RadioButton radioCopy;
        private Label lblConflictHandling;
        private Button btnSupportedFormats;
        private GroupBox groupBoxFunctions;
        private TableLayoutPanel tableLayoutPanelFunctions;
        private GroupBox groupBoxActions;
        private TableLayoutPanel tableLayoutPanelActions;
        private GroupBox groupBoxStats;
        private ProgressBar progressBar;
        private Label lblStats;
        private Label lblProgress;
    }
}
