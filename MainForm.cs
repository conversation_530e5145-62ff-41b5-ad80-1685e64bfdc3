using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using PPTPiliangChuli.Forms;
using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    public partial class MainForm : Form
    {
        // 功能按钮数组
        private CheckBox[] functionCheckBoxes = null!;
        private Button[] functionButtons = null!;

        // 操作按钮数组
        private Button[] actionButtons = null!;

        // 功能名称
        private readonly string[] functionNames = {
            "页面设置", "内容删除设置", "内容替换设置",
            "PPT格式设置", "匹配段落格式", "页眉页脚设置",
            "文档属性", "文件名替换", "PPT格式转换"
        };

        // 操作按钮名称
        private readonly string[] actionNames = {
            "开始处理", "定时处理", "停止处理",
            "日志设置", "清空日志", "导出配置",
            "导入配置", "打开源目录", "打开输出目录"
        };

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomControls();
            InitializeFileProcessingService();
            LoadConfiguration();
            ApplyModernStyle();
        }

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            InitializeFunctionButtons();
            InitializeActionButtons();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化功能按钮
        /// </summary>
        private void InitializeFunctionButtons()
        {
            functionCheckBoxes = new CheckBox[9];
            functionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                // 创建复选框
                var checkBox = new CheckBox
                {
                    Name = $"chkFunction{i}",
                    Text = "",
                    Size = new Size(20, 20),
                    Anchor = AnchorStyles.Left | AnchorStyles.Top,
                    Checked = false
                };

                // 创建功能按钮
                var button = new Button
                {
                    Name = $"btnFunction{i}",
                    Text = functionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard
                };

                // 创建面板容器，设置边距
                var panel = new Panel
                {
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    Margin = new Padding(5, 3, 5, 3),
                    Padding = new Padding(5, 5, 5, 5)
                };

                // 设置控件位置和大小
                checkBox.Location = new Point(5, 17);
                button.Location = new Point(30, 5);
                button.Size = new Size(panel.Width - 40, panel.Height - 10);

                panel.Controls.Add(checkBox);
                panel.Controls.Add(button);

                tableLayoutPanelFunctions.Controls.Add(panel, col, row);

                functionCheckBoxes[i] = checkBox;
                functionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnFunctionButtonClick(index);
            }
        }

        /// <summary>
        /// 初始化操作按钮
        /// </summary>
        private void InitializeActionButtons()
        {
            actionButtons = new Button[9];

            for (int i = 0; i < 9; i++)
            {
                int row = i / 3;
                int col = i % 3;

                var button = new Button
                {
                    Name = $"btnAction{i}",
                    Text = actionNames[i],
                    Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom,
                    UseVisualStyleBackColor = true,
                    Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                    FlatStyle = FlatStyle.Standard,
                    Margin = new Padding(8, 5, 8, 5)
                };

                tableLayoutPanelActions.Controls.Add(button, col, row);
                actionButtons[i] = button;

                // 绑定事件
                int index = i; // 闭包变量
                button.Click += (s, e) => OnActionButtonClick(index);
            }

            // 设置停止处理按钮初始状态为禁用
            actionButtons[2].Enabled = false; // 停止处理
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 路径浏览按钮
            btnBrowseSource.Click += BtnBrowseSource_Click;
            btnBrowseOutput.Click += BtnBrowseOutput_Click;

            // 路径文本框变更事件
            txtSourcePath.TextChanged += TxtSourcePath_TextChanged;

            // 选项变更事件
            chkIncludeSubfolders.CheckedChanged += ChkIncludeSubfolders_CheckedChanged;

            // 全选/取消全选按钮
            btnSelectAll.Click += BtnSelectAll_Click;
            btnDeselectAll.Click += BtnDeselectAll_Click;

            // 支持格式设定按钮
            btnSupportedFormats.Click += BtnSupportedFormats_Click;

            // 窗体关闭事件
            this.FormClosing += MainForm_FormClosing;
        }

        /// <summary>
        /// 功能按钮点击事件
        /// </summary>
        private void OnFunctionButtonClick(int index)
        {
            MessageBox.Show($"打开{functionNames[index]}设置窗口", "功能设置",
                MessageBoxButtons.OK, MessageBoxIcon.Information);

            // TODO: 根据index打开对应的功能设置窗体
        }

        /// <summary>
        /// 操作按钮点击事件
        /// </summary>
        private void OnActionButtonClick(int index)
        {
            switch (index)
            {
                case 0: // 开始处理
                    StartProcessing();
                    break;
                case 1: // 定时处理
                    ShowScheduleDialog();
                    break;
                case 2: // 停止处理
                    StopProcessing();
                    break;
                case 3: // 日志设置
                    ShowLogSettings();
                    break;
                case 4: // 清空日志
                    ClearLogs();
                    break;
                case 5: // 导出配置
                    ExportConfiguration();
                    break;
                case 6: // 导入配置
                    ImportConfiguration();
                    break;
                case 7: // 打开源目录
                    OpenSourceDirectory();
                    break;
                case 8: // 打开输出目录
                    OpenOutputDirectory();
                    break;
            }
        }

        /// <summary>
        /// 浏览源目录
        /// </summary>
        private void BtnBrowseSource_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择源目录";
            dialog.UseDescriptionForTitle = true;

            if (!string.IsNullOrEmpty(txtSourcePath.Text) && Directory.Exists(txtSourcePath.Text))
            {
                dialog.SelectedPath = txtSourcePath.Text;
            }

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtSourcePath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 源路径文本变更事件
        /// </summary>
        private void TxtSourcePath_TextChanged(object? sender, EventArgs e)
        {
            UpdateFileCountDisplay();
        }

        /// <summary>
        /// 包含子目录选项变更事件
        /// </summary>
        private void ChkIncludeSubfolders_CheckedChanged(object? sender, EventArgs e)
        {
            UpdateFileCountDisplay();
        }

        /// <summary>
        /// 浏览输出目录
        /// </summary>
        private void BtnBrowseOutput_Click(object? sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择输出目录";
            dialog.UseDescriptionForTitle = true;

            if (!string.IsNullOrEmpty(txtOutputPath.Text) && Directory.Exists(txtOutputPath.Text))
            {
                dialog.SelectedPath = txtOutputPath.Text;
            }

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                txtOutputPath.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 全选功能
        /// </summary>
        private void BtnSelectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = true;
            }
        }

        /// <summary>
        /// 取消全选功能
        /// </summary>
        private void BtnDeselectAll_Click(object? sender, EventArgs e)
        {
            foreach (var checkBox in functionCheckBoxes)
            {
                checkBox.Checked = false;
            }
        }

        /// <summary>
        /// 初始化文件处理服务
        /// </summary>
        private void InitializeFileProcessingService()
        {
            var service = FileProcessingService.Instance;
            service.ProgressChanged += OnProcessingProgressChanged;
            service.ProcessCompleted += OnProcessingCompleted;
            service.FileProcessed += OnFileProcessed;
        }

        /// <summary>
        /// 应用现代化样式
        /// </summary>
        private void ApplyModernStyle()
        {
            // 主窗体样式
            this.BackColor = Color.FromArgb(248, 249, 250);

            // GroupBox样式
            foreach (Control control in this.Controls)
            {
                if (control is GroupBox groupBox)
                {
                    groupBox.ForeColor = Color.FromArgb(33, 37, 41);
                    groupBox.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
                }
            }

            // 按钮样式
            ApplyButtonStyles();
        }

        /// <summary>
        /// 应用按钮样式
        /// </summary>
        private void ApplyButtonStyles()
        {
            // 浏览按钮
            btnBrowseSource.BackColor = Color.FromArgb(0, 123, 255);
            btnBrowseSource.ForeColor = Color.White;
            btnBrowseSource.FlatStyle = FlatStyle.Flat;
            btnBrowseSource.FlatAppearance.BorderSize = 0;

            btnBrowseOutput.BackColor = Color.FromArgb(0, 123, 255);
            btnBrowseOutput.ForeColor = Color.White;
            btnBrowseOutput.FlatStyle = FlatStyle.Flat;
            btnBrowseOutput.FlatAppearance.BorderSize = 0;

            // 支持格式按钮
            btnSupportedFormats.BackColor = Color.FromArgb(40, 167, 69);
            btnSupportedFormats.ForeColor = Color.White;
            btnSupportedFormats.FlatStyle = FlatStyle.Flat;
            btnSupportedFormats.FlatAppearance.BorderSize = 0;

            // 全选/取消全选按钮
            btnSelectAll.BackColor = Color.FromArgb(40, 167, 69);
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.FlatAppearance.BorderSize = 0;

            btnDeselectAll.BackColor = Color.FromArgb(220, 53, 69);
            btnDeselectAll.ForeColor = Color.White;
            btnDeselectAll.FlatStyle = FlatStyle.Flat;
            btnDeselectAll.FlatAppearance.BorderSize = 0;
        }

        /// <summary>
        /// 支持格式设定按钮点击事件
        /// </summary>
        private void BtnSupportedFormats_Click(object? sender, EventArgs e)
        {
            using var form = new SupportedFormatsForm();
            if (form.ShowDialog(this) == DialogResult.OK)
            {
                // 格式设置已更新，可以在这里做一些后续处理
                UpdateFileCountDisplay();
            }
        }

        /// <summary>
        /// 更新文件数量显示
        /// </summary>
        private void UpdateFileCountDisplay()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSourcePath.Text) || !Directory.Exists(txtSourcePath.Text))
                {
                    lblStats.Text = "总文件数: 0  成功处理: 0 (0.0%)  处理失败: 0 (0.0%)  开始时间: --  预计结束时间: --";
                    return;
                }

                var config = ConfigService.Instance.GetConfig();
                var files = FileProcessingService.Instance.GetPowerPointFiles(
                    txtSourcePath.Text,
                    chkIncludeSubfolders.Checked,
                    config.ProcessSettings.SupportedFormats);

                lblStats.Text = $"总文件数: {files.Count}  成功处理: 0 (0.0%)  处理失败: 0 (0.0%)  开始时间: --  预计结束时间: --";
            }
            catch (Exception ex)
            {
                lblStats.Text = $"扫描文件时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 开始处理
        /// </summary>
        private async void StartProcessing()
        {
            if (string.IsNullOrWhiteSpace(txtSourcePath.Text))
            {
                MessageBox.Show("请选择源目录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!chkDeleteSource.Checked && string.IsNullOrWhiteSpace(txtOutputPath.Text))
            {
                MessageBox.Show("请选择输出目录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!Directory.Exists(txtSourcePath.Text))
            {
                MessageBox.Show("源目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var config = ConfigService.Instance.GetConfig();
            if (config.ProcessSettings.SupportedFormats.Count == 0)
            {
                MessageBox.Show("请先设置支持的文件格式", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 创建处理选项
            var options = new ProcessingOptions
            {
                SourcePath = txtSourcePath.Text,
                OutputPath = txtOutputPath.Text,
                IncludeSubfolders = chkIncludeSubfolders.Checked,
                KeepDirectoryStructure = chkKeepStructure.Checked,
                CopyFiles = radioCopy.Checked,
                ProcessSourceDirectly = chkDeleteSource.Checked,
                ThreadCount = (int)numThreadCount.Value,
                RetryCount = (int)numRetryCount.Value,
                BatchSize = (int)numBatchSize.Value,
                SupportedFormats = config.ProcessSettings.SupportedFormats.ToList()
            };

            try
            {
                // 更新UI状态
                SetProcessingState(true);

                // 开始处理
                await FileProcessingService.Instance.StartProcessingAsync(options);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理过程中出现错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        /// <summary>
        /// 设置处理状态
        /// </summary>
        private void SetProcessingState(bool isProcessing)
        {
            // 禁用/启用相关控件
            btnBrowseSource.Enabled = !isProcessing;
            btnBrowseOutput.Enabled = !isProcessing;
            txtSourcePath.Enabled = !isProcessing;
            txtOutputPath.Enabled = !isProcessing;
            chkIncludeSubfolders.Enabled = !isProcessing;
            chkKeepStructure.Enabled = !isProcessing;
            radioCopy.Enabled = !isProcessing;
            radioMove.Enabled = !isProcessing;
            chkDeleteSource.Enabled = !isProcessing;
            numThreadCount.Enabled = !isProcessing;
            numRetryCount.Enabled = !isProcessing;
            numBatchSize.Enabled = !isProcessing;
            btnSupportedFormats.Enabled = !isProcessing;

            // 更新操作按钮状态
            actionButtons[0].Enabled = !isProcessing; // 开始处理
            actionButtons[2].Enabled = isProcessing;  // 停止处理

            // 更新进度条
            if (!isProcessing)
            {
                progressBar.Value = 0;
                lblProgress.Text = "处理速度: 0 文件/分钟    处理耗时: 00:00:00";
            }
        }

        /// <summary>
        /// 显示定时处理对话框
        /// </summary>
        private void ShowScheduleDialog()
        {
            MessageBox.Show("定时处理功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 停止处理
        /// </summary>
        private void StopProcessing()
        {
            var result = MessageBox.Show("确定要停止当前处理任务吗？", "确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                FileProcessingService.Instance.StopProcessing();
            }
        }

        /// <summary>
        /// 处理进度变更事件
        /// </summary>
        private void OnProcessingProgressChanged(object? sender, ProcessProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnProcessingProgressChanged(sender, e)));
                return;
            }

            // 更新进度条
            progressBar.Value = Math.Min(e.ProgressPercentage, 100);

            // 更新统计信息
            var stats = e.Stats;
            var endTime = stats.EndTime ?? DateTime.Now;
            var estimatedEndTime = stats.FilesPerMinute > 0 && stats.TotalFiles > 0
                ? stats.StartTime.AddMinutes((stats.TotalFiles - stats.SuccessCount - stats.FailureCount) / stats.FilesPerMinute)
                : (DateTime?)null;

            lblStats.Text = $"总文件数: {stats.TotalFiles}  " +
                           $"成功处理: {stats.SuccessCount} ({stats.SuccessRate:F1}%)  " +
                           $"处理失败: {stats.FailureCount} ({stats.FailureRate:F1}%)  " +
                           $"开始时间: {stats.StartTime:HH:mm:ss}  " +
                           $"预计结束时间: {(estimatedEndTime?.ToString("HH:mm:ss") ?? "--")}";

            // 更新处理速度和耗时
            lblProgress.Text = $"处理速度: {stats.FilesPerMinute:F1} 文件/分钟    " +
                              $"处理耗时: {stats.ElapsedTime:hh\\:mm\\:ss}";
        }

        /// <summary>
        /// 处理完成事件
        /// </summary>
        private void OnProcessingCompleted(object? sender, ProcessCompletedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnProcessingCompleted(sender, e)));
                return;
            }

            SetProcessingState(false);

            var stats = e.Stats;
            var message = $"{e.Message}\n\n" +
                         $"处理统计:\n" +
                         $"总文件数: {stats.TotalFiles}\n" +
                         $"成功处理: {stats.SuccessCount} ({stats.SuccessRate:F1}%)\n" +
                         $"处理失败: {stats.FailureCount} ({stats.FailureRate:F1}%)\n" +
                         $"总耗时: {stats.ElapsedTime:hh\\:mm\\:ss}\n" +
                         $"平均速度: {stats.FilesPerMinute:F1} 文件/分钟";

            MessageBox.Show(message, "处理完成", MessageBoxButtons.OK,
                stats.FailureCount > 0 ? MessageBoxIcon.Warning : MessageBoxIcon.Information);
        }

        /// <summary>
        /// 文件处理事件
        /// </summary>
        private void OnFileProcessed(object? sender, FileProcessedEventArgs e)
        {
            // 可以在这里记录详细的文件处理日志
            // 或者更新UI显示当前正在处理的文件
        }

        /// <summary>
        /// 显示日志设置
        /// </summary>
        private void ShowLogSettings()
        {
            MessageBox.Show("日志设置功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 清空日志
        /// </summary>
        private void ClearLogs()
        {
            var result = MessageBox.Show("确定要清空所有日志文件吗？", "确认",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: 实现清空日志逻辑
                MessageBox.Show("日志已清空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// 导出配置
        /// </summary>
        private void ExportConfiguration()
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择配置导出目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                // 先保存当前配置
                SaveConfiguration();

                if (ConfigService.Instance.ExportConfig(dialog.SelectedPath))
                {
                    MessageBox.Show("配置导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("配置导出失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 导入配置
        /// </summary>
        private void ImportConfiguration()
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "选择配置导入目录";
            dialog.UseDescriptionForTitle = true;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                var result = MessageBox.Show("导入配置将覆盖当前设置，是否继续？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (ConfigService.Instance.ImportConfig(dialog.SelectedPath))
                    {
                        MessageBox.Show("配置导入成功！重新加载界面设置...", "提示",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadConfiguration();
                    }
                    else
                    {
                        MessageBox.Show("配置导入失败！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 打开源目录
        /// </summary>
        private void OpenSourceDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtSourcePath.Text) && Directory.Exists(txtSourcePath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtSourcePath.Text);
            }
            else
            {
                MessageBox.Show("源目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 打开输出目录
        /// </summary>
        private void OpenOutputDirectory()
        {
            if (!string.IsNullOrWhiteSpace(txtOutputPath.Text) && Directory.Exists(txtOutputPath.Text))
            {
                System.Diagnostics.Process.Start("explorer.exe", txtOutputPath.Text);
            }
            else
            {
                MessageBox.Show("输出目录不存在", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 加载路径设置
                txtSourcePath.Text = config.PathSettings.SourcePath;
                txtOutputPath.Text = config.PathSettings.OutputPath;
                chkIncludeSubfolders.Checked = config.PathSettings.IncludeSubfolders;
                chkKeepStructure.Checked = config.PathSettings.KeepDirectoryStructure;

                // 加载处理设置
                radioCopy.Checked = config.ProcessSettings.CopyFiles;
                radioMove.Checked = !config.ProcessSettings.CopyFiles;
                chkDeleteSource.Checked = config.ProcessSettings.ProcessSourceDirectly;
                numThreadCount.Value = config.ProcessSettings.ThreadCount;
                numRetryCount.Value = config.ProcessSettings.RetryCount;
                numBatchSize.Value = config.ProcessSettings.BatchSize;

                // 加载功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    if (config.FunctionEnabled.ContainsKey(functionNames[i]))
                    {
                        functionCheckBoxes[i].Checked = config.FunctionEnabled[functionNames[i]];
                    }
                }

                // 加载窗体设置
                if (config.FormSettings.LocationX >= 0 && config.FormSettings.LocationY >= 0)
                {
                    this.StartPosition = FormStartPosition.Manual;
                    this.Location = new Point(config.FormSettings.LocationX, config.FormSettings.LocationY);
                }

                this.Size = new Size(config.FormSettings.Width, config.FormSettings.Height);
                this.WindowState = (FormWindowState)config.FormSettings.WindowState;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置加载失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();

                // 保存路径设置
                config.PathSettings.SourcePath = txtSourcePath.Text;
                config.PathSettings.OutputPath = txtOutputPath.Text;
                config.PathSettings.IncludeSubfolders = chkIncludeSubfolders.Checked;
                config.PathSettings.KeepDirectoryStructure = chkKeepStructure.Checked;

                // 保存处理设置
                config.ProcessSettings.CopyFiles = radioCopy.Checked;
                config.ProcessSettings.ProcessSourceDirectly = chkDeleteSource.Checked;
                config.ProcessSettings.ThreadCount = (int)numThreadCount.Value;
                config.ProcessSettings.RetryCount = (int)numRetryCount.Value;
                config.ProcessSettings.BatchSize = (int)numBatchSize.Value;

                // 保存功能启用状态
                for (int i = 0; i < functionNames.Length; i++)
                {
                    config.FunctionEnabled[functionNames[i]] = functionCheckBoxes[i].Checked;
                }

                // 保存窗体设置
                if (this.WindowState == FormWindowState.Normal)
                {
                    config.FormSettings.LocationX = this.Location.X;
                    config.FormSettings.LocationY = this.Location.Y;
                    config.FormSettings.Width = this.Size.Width;
                    config.FormSettings.Height = this.Size.Height;
                }
                config.FormSettings.WindowState = (int)this.WindowState;

                ConfigService.Instance.UpdateConfig(config);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"配置保存失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            SaveConfiguration();
        }
    }
}
